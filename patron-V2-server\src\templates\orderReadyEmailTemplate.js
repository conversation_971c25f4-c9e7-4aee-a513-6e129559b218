// Email template for order ready notifications with embedded review form
export const generateOrderReadyEmailTemplate = (
  customerName,
  orderType,
  orderNo,
  businessName,
  deviceInfo,
  apiBaseUrl,
  orderId,
  customerId
) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';

  return `
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: 'Poppins', sans-serif;
          background-color: #f8f8f8;
          margin: 0;
          padding: 0;
        }
        .container {
          width: 90%;
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header-bar {
          height: 4rem;
          background-color: #068af5;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        .logo {
          display: block;
          margin: 30px auto;
          max-width: 180px;
        }
        .order-details {
          background-color: #e8f4fd;
          padding: 15px;
          border-left: 4px solid #068af5;
          margin: 20px 0;
          border-radius: 5px;
        }
        .order-details h3 {
          margin: 0 0 10px;
          color: #068af5;
        }
        .review-section {
          background-color: #f0f8ff;
          padding: 25px;
          border-radius: 8px;
          border: 2px solid #068af5;
          margin: 30px 0;
        }
        .review-form {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .rating-label {
          font-weight: bold;
          margin-bottom: 10px;
          display: block;
          color: #333;
        }
        .star-rating {
          text-align: center;
          margin-bottom: 20px;
        }
        .star {
          font-size: 35px;
          color: #ddd;
          cursor: pointer !important;
          margin: 0 3px;
          transition: color 0.2s;
          display: inline-block;
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }
        .star:hover {
          color: #ffc107 !important;
        }
        .star.active {
          color: #ffc107 !important;
        }
        .star.filled {
          color: #ffc107 !important;
        }
        .rating-buttons {
          text-align: center;
          margin-bottom: 20px;
        }
        .rating-btn {
          background: #f8f9fa;
          border: 2px solid #ddd;
          padding: 10px 15px;
          margin: 0 5px;
          cursor: pointer !important;
          border-radius: 5px;
          font-size: 16px;
          transition: all 0.2s;
          display: inline-block;
        }
        .rating-btn:hover {
          background: #ffc107;
          border-color: #ffc107;
          color: white;
        }
        .rating-btn.selected {
          background: #ffc107;
          border-color: #ffc107;
          color: white;
        }
        #selectedRating {
          display: none;
        }
        .form-group {
          margin-bottom: 15px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          min-height: 80px;
          resize: vertical;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 12px 30px;
          border: none;
          border-radius: 5px;
          font-weight: bold;
          cursor: pointer;
          font-size: 16px;
          transition: background-color 0.3s;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #888;
          margin-top: 30px;
        }
        .social-icons img {
          width: 20px;
          margin: 0 5px;
        }
        @media screen and (max-width: 600px) {
          .container {
            padding: 20px;
          }
          .logo {
            max-width: 140px;
          }
        }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    </head>
    <body>
      <div class="header-bar"></div>
      <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>${customerName}</strong>,</p>

        <div class="order-details">
          <h3>🎉 Great News! Your Order is Ready</h3>
          <p><strong>Order Number:</strong> ${orderNo}</p>
          <p><strong>Status:</strong> Your order is <strong>${readyMessage}</strong>.</p>
          <p>${actionMessage}</p>
        </div>

        <p>Thank you for choosing <strong>${businessName}</strong>.</p>
        ${isDelivery 
          ? `<p>Our delivery team will be reaching out to you shortly.</p>` 
          : `<p>Please visit our location to collect your order at your convenience.</p>`}

        <div class="review-section">
          <h3>📝 We Value Your Feedback!</h3>
          <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

          <div class="review-form">
            <form id="reviewForm" action="${apiBaseUrl}/api/v1/device/email-review" method="POST">
              <input type="hidden" name="deviceId" value="${deviceInfo?._id || ''}">
              <input type="hidden" name="customerId" value="${customerId}">
              <input type="hidden" name="orderId" value="${orderId}">

              <label class="rating-label">⭐ Rate Your Overall Experience</label>

              <!-- Star Display -->
              <div class="star-rating" id="starDisplay">
                <span class="star" data-rating="1">★</span>
                <span class="star" data-rating="2">★</span>
                <span class="star" data-rating="3">★</span>
                <span class="star" data-rating="4">★</span>
                <span class="star" data-rating="5">★</span>
              </div>

              <!-- Button Fallback -->
              <div class="rating-buttons">
                <button type="button" class="rating-btn" data-rating="1">1 ⭐</button>
                <button type="button" class="rating-btn" data-rating="2">2 ⭐</button>
                <button type="button" class="rating-btn" data-rating="3">3 ⭐</button>
                <button type="button" class="rating-btn" data-rating="4">4 ⭐</button>
                <button type="button" class="rating-btn" data-rating="5">5 ⭐</button>
              </div>

              <input type="hidden" name="rating" id="selectedRating" value="" required>

              <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience at ${businessName}..."></textarea>
              </div>

              <input type="submit" class="submit-button" value="Submit Review" />
            </form>
            <p style="font-size: 13px; color: #555;"><em>Thank you for your feedback!</em></p>
          </div>
        </div>

        <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
          <p>Follow us:</p>
          <div class="social-icons">
            <a href="https://www.linkedin.com/company/patronworks/"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn" /></a>
            <a href="https://www.facebook.com/patronworks"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook" /></a>
          </div>
          <p>&copy; ${new Date().getFullYear()} ${businessName}. All rights reserved.</p>
        </div>
      </div>

      <script>
        // Rating functionality
        const stars = document.querySelectorAll('.star');
        const buttons = document.querySelectorAll('.rating-btn');
        const ratingInput = document.getElementById('selectedRating');
        let selectedRating = 0;

        // Star click events
        stars.forEach((star) => {
          star.style.cursor = 'pointer';

          star.addEventListener('click', function(e) {
            e.preventDefault();
            selectedRating = parseInt(this.getAttribute('data-rating'));
            ratingInput.value = selectedRating;
            updateDisplay();
            console.log('Star clicked, rating:', selectedRating);
          });

          star.addEventListener('mouseenter', function() {
            const hoverRating = parseInt(this.getAttribute('data-rating'));
            highlightStars(hoverRating);
          });
        });

        // Button click events
        buttons.forEach((button) => {
          button.addEventListener('click', function(e) {
            e.preventDefault();
            selectedRating = parseInt(this.getAttribute('data-rating'));
            ratingInput.value = selectedRating;
            updateDisplay();
            console.log('Button clicked, rating:', selectedRating);
          });
        });

        // Reset to selected rating on mouse leave
        const starContainer = document.querySelector('.star-rating');
        if (starContainer) {
          starContainer.addEventListener('mouseleave', function() {
            updateDisplay();
          });
        }

        function highlightStars(rating) {
          stars.forEach((star, index) => {
            if (index < rating) {
              star.classList.add('active');
              star.classList.remove('filled');
            } else {
              star.classList.remove('active');
              star.classList.remove('filled');
            }
          });
        }

        function updateDisplay() {
          // Update stars
          stars.forEach((star, index) => {
            if (index < selectedRating) {
              star.classList.add('filled');
              star.classList.remove('active');
            } else {
              star.classList.remove('filled');
              star.classList.remove('active');
            }
          });

          // Update buttons
          buttons.forEach((button) => {
            const buttonRating = parseInt(button.getAttribute('data-rating'));
            if (buttonRating === selectedRating) {
              button.classList.add('selected');
            } else {
              button.classList.remove('selected');
            }
          });
        }

        // Form validation
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
          console.log('Form submitted, rating:', selectedRating);
          if (!selectedRating || selectedRating === 0) {
            e.preventDefault();
            alert('Please select a star rating before submitting your review.');
            return false;
          }
        });

        // Initialize display
        document.addEventListener('DOMContentLoaded', function() {
          console.log('DOM loaded, initializing rating system');
          updateDisplay();
        });
      </script>
    </body>
  </html>
  `;
};

import device from '../models/device.js';
import mongoose from 'mongoose';

export const getDevices = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter);
    res.send(data);
}

export const getDevicespatronpal = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter).populate("userId").populate({
        path: 'reviews.customerId',
        select: 'FirstName LastName profile_pic'
    }).populate({
        path: 'favorites.customerId',
        select: 'FirstName LastName profile_pic'
    })
    .exec();
    res.send(data);
}

export const getDevice = async (req, res) => {
    let data = await device.findOne(req.params);
    res.send(data);
}

export const postDevice = async (req, res) => {
    const { active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country,delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType , Streetaddress} = req.body;
    const image = req.file ? req.file.location : null
    const data = await new device({ active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country, image, delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType ,Streetaddress});
    await data.save().then(result => {
        console.log(result, "Device data save to database")
        res.json({
            _id: result._id,
            name: result.name,
            active: result.active,
            userId: result.userId,
            image: result.image,
            Line1: result.Line1,
            line2: result.Line2,
            City: result.City,
            Phoneno: result.Phoneno,
            State: result.State,
            PostalCode: result.PostalCode,
            Country:result.Country,
            delivery: result.delivery,
            deliveryStartTime: result.deliveryStartTime,
            deliveryEndTime: result.deliveryEndTime,
            ChargesperKm: result.ChargesperKm, 
            ChargesFreeKm: result.ChargesFreeKm,
            pickupStartTime: result.pickupStartTime,
            pickupEndTime: result.pickupEndTime,
            businessType: result.businessType,
            Streetaddress: result.Streetaddress,
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
export const updateDevice = async (req, res) => {
    console.log(req.params._id)
    let image
    if(req.file){
         image = req.file ? req.file.location : null
    }
    console.log("body /: ", req.body)

    let data = await device.findByIdAndUpdate(
        { _id: req.params._id },
        {
            $set: req.body, image: image,
        },  { new: true });
    if (data) {
        res.send({ data, message: "device data updated successfully" });
    }
    else {
        res.send({ message: "device data cannot be updated successfully" })
    }
}

// Get device information for review form by userId
export const getDeviceForReview = async (req, res) => {
    const { userId } = req.params;

    // Validate the userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ message: "Invalid user ID format." });
    }

    try {
        // Find the device by userId
        const Device = await device.findOne({ userId }).populate('userId', 'name email businessName');
        if (!Device) {
            return res.status(404).json({ message: "Device not found for this user" });
        }

        // Return device information needed for review form
        const deviceInfo = {
            _id: Device._id,
            name: Device.name,
            businessName: Device.userId?.businessName || Device.name,
            address: `${Device.Line1 || ''} ${Device.Line2 || ''}, ${Device.City || ''}, ${Device.State || ''} ${Device.PostalCode || ''}`.trim(),
            phone: Device.Phoneno,
            image: Device.image,
            userId: Device.userId._id,
            userEmail: Device.userId.email
        };

        res.json({
            success: true,
            device: deviceInfo,
            message: "Device information retrieved successfully"
        });
    } catch (error) {
        console.error("Error getting device for review:", error);
        res.status(500).json({ message: "Server error" });
    }
};

// Handle review submission from email form
export const submitEmailReview = async (req, res) => {
    try {
        console.log('Email review request body:', req.body);
        const { deviceId, customerId, orderId, rating, testimonial } = req.body;

        if (!deviceId || !customerId) {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Device ID and Customer ID are required.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Validate that rating is provided and is valid
        if (!rating || rating === '' || rating === '0') {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Please select a star rating before submitting your review.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Validate rating is between 1-5
        const ratingNum = parseInt(rating);
        console.log('Rating received:', rating, 'Parsed to:', ratingNum, 'Type:', typeof ratingNum);

        if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
            return res.status(400).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Rating must be between 1 and 5 stars.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Find the device
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).send(`
                <html>
                <head><title>Review Submission Error</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #dc3545;">❌ Error</h2>
                    <p>Restaurant not found.</p>
                    <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
                </body>
                </html>
            `);
        }

        // Check if a review from the same customer already exists
        const existingReview = Device.reviews.find(review => review.customerId && review.customerId.toString() === customerId);
        if (existingReview) {
            return res.status(400).send(`
                <html>
                <head><title>Review Already Submitted</title></head>
                <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                    <h2 style="color: #ffc107;">⚠️ Already Reviewed</h2>
                    <p>You have already submitted a review for this restaurant.</p>
                    <p>Thank you for your previous feedback!</p>
                </body>
                </html>
            `);
        }

        // Create the new review object with all required fields
        const newReview = {
            rating: ratingNum, // Use the validated rating number
            testimonial: testimonial || '',
            customerId: new mongoose.Types.ObjectId(customerId), // Convert to ObjectId
            orderId: orderId || '',
            createdDate: new Date()
        };

        console.log('Creating review with data:', newReview);

        // Add the review to the device and save
        Device.reviews.push(newReview);

        // Validate the device before saving
        const validationError = Device.validateSync();
        if (validationError) {
            console.error('Validation error:', validationError);
            throw validationError;
        }

        await Device.save();

        // Redirect to thank you page with review data
        const restaurantName = Device.name || Device.businessName || 'Restaurant';

        // Instead of showing confirmation, redirect to thank you page
        res.redirect(`/api/v1/device/thank-you?deviceId=${deviceId}&rating=${ratingNum}&testimonial=${encodeURIComponent(testimonial || '')}&restaurantName=${encodeURIComponent(restaurantName)}`);
                    </div>
                </div>
            </body>
            </html>
        `);

    } catch (error) {
        console.error("Error in submitEmailReview:", error);
        res.status(500).send(`
            <html>
            <head><title>Review Submission Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                <h2 style="color: #dc3545;">❌ Server Error</h2>
                <p>There was an error submitting your review. Please try again later.</p>
                <p><a href="javascript:history.back()" style="color: #068af5;">Go Back</a></p>
            </body>
            </html>
        `);
    }
};

// Thank you page after review submission
export const showThankYouPage = async (req, res) => {
    try {
        const { deviceId, rating, testimonial, restaurantName } = req.query;

        if (!deviceId || !rating) {
            return res.status(400).send('Missing required parameters');
        }

        // Get all reviews for this restaurant to show
        const Device = await device.findById(deviceId).populate('reviews.customerId', 'firstName lastName');
        if (!Device) {
            return res.status(404).send('Restaurant not found');
        }

        const starsDisplay = '⭐'.repeat(parseInt(rating));
        const allReviews = Device.reviews || [];

        // Generate reviews HTML
        const reviewsHtml = allReviews.length > 0 ? allReviews.map(review => {
            const reviewStars = '⭐'.repeat(review.rating || 0);
            const customerName = review.customerId ?
                `${review.customerId.firstName || ''} ${review.customerId.lastName || ''}`.trim() :
                'Anonymous';
            const reviewDate = review.createdDate ? new Date(review.createdDate).toLocaleDateString() : '';

            return `
                <div style="border: 1px solid #eee; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong style="color: #333;">${customerName}</strong>
                        <span style="color: #666; font-size: 12px;">${reviewDate}</span>
                    </div>
                    <div style="margin: 8px 0; font-size: 18px;">${reviewStars} (${review.rating}/5)</div>
                    ${review.testimonial ? `<p style="color: #555; margin: 8px 0; font-style: italic;">"${review.testimonial}"</p>` : ''}
                </div>
            `;
        }).join('') : '<p style="color: #666; text-align: center;">No reviews yet. Be the first to review!</p>';

        res.send(`
            <html>
            <head>
                <title>Thank You - Review Submitted</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: 'Poppins', Arial, sans-serif;
                        background-color: #f8f9fa;
                        margin: 0;
                        padding: 20px;
                    }
                    .container {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 40px;
                        border-radius: 10px;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    }
                    .thank-you-section {
                        text-align: center;
                        margin-bottom: 40px;
                        padding: 30px;
                        background: linear-gradient(135deg, #068af5, #0056b3);
                        color: white;
                        border-radius: 10px;
                    }
                    .your-review {
                        background-color: #e8f4fd;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 20px 0;
                        border-left: 4px solid #068af5;
                    }
                    .reviews-section {
                        margin-top: 40px;
                    }
                    .reviews-container {
                        max-height: 400px;
                        overflow-y: auto;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 15px;
                        background: white;
                    }
                    .button {
                        display: inline-block;
                        background-color: #068af5;
                        color: white;
                        padding: 12px 30px;
                        text-decoration: none;
                        border-radius: 5px;
                        font-weight: bold;
                        margin: 10px;
                        transition: background-color 0.3s;
                    }
                    .button:hover {
                        background-color: #0056b3;
                    }
                    .button.secondary {
                        background-color: #6c757d;
                    }
                    .button.secondary:hover {
                        background-color: #545b62;
                    }
                </style>
                <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
            </head>
            <body>
                <div class="container">
                    <div class="thank-you-section">
                        <h1 style="margin: 0 0 15px 0;">🎉 Thank You!</h1>
                        <h2 style="margin: 0 0 20px 0; font-weight: normal;">Your review has been submitted successfully</h2>
                        <p style="margin: 0; opacity: 0.9;">We appreciate you taking the time to share your experience!</p>
                    </div>

                    <div class="your-review">
                        <h3 style="color: #068af5; margin-top: 0;">📝 Your Review for ${decodeURIComponent(restaurantName)}</h3>
                        <div style="font-size: 24px; margin: 15px 0;">${starsDisplay} (${rating}/5)</div>
                        ${testimonial && testimonial !== 'undefined' ? `<p style="margin: 15px 0; font-style: italic; color: #555;">"${decodeURIComponent(testimonial)}"</p>` : ''}
                        <p style="color: #666; margin: 10px 0;">Your feedback helps other customers and improves our service!</p>
                    </div>

                    <div class="reviews-section">
                        <h3 style="color: #333; border-bottom: 2px solid #068af5; padding-bottom: 10px;">
                            👥 What Other Customers Are Saying
                        </h3>
                        <div class="reviews-container">
                            ${reviewsHtml}
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 1px solid #eee;">
                        <p style="color: #666; margin-bottom: 20px;">Thank you for choosing ${decodeURIComponent(restaurantName)}!</p>
                        <a href="https://patronpal.com" class="button">🏠 Back to PatronPal</a>
                        <a href="javascript:window.close()" class="button secondary">✖️ Close Window</a>
                    </div>
                </div>
            </body>
            </html>
        `);

    } catch (error) {
        console.error("Error in showThankYouPage:", error);
        res.status(500).send(`
            <html>
            <head><title>Error</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
                <h2 style="color: #dc3545;">❌ Error</h2>
                <p>There was an error loading the page. Please try again later.</p>
            </body>
            </html>
        `);
    }
};

export const postReview = async (req, res) => {
    const { deviceId, food, service, ambiance, testimonial,  customerId } = req.body;

    // Validate the deviceId
    if (!mongoose.Types.ObjectId.isValid(deviceId)) {
        return res.status(400).json({ message: "Invalid device ID format." });
    }

    try {
        // Find the device by ID
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).json({ message: "Device not found" });
        }

        // Check if a review from the same customer already exists
        const existingReview = Device.reviews.find(review => review.customerId == customerId);
        if (existingReview) {
            return res.status(400).json({ message: "Review from this customer already exists." });
        }

        // Create and add the new review
        const newReview = {
            food,
            service,
            ambiance,
            testimonial,
            customerId
        };

        Device.reviews.push(newReview);
        await Device.save();

        res.json({ message: "Review added successfully", review: newReview });
    } catch (error) {
        console.error("Error adding review:", error);
        res.status(500).json({ message: "Server error" });
    }
};


// Get all devices where any review has the given customerId    
export const getallcustomer = async (req, res) => {
    const { customerId } = req.params;

    // Validate customerId
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID format." });
    }

    try {
        // Find all devices where reviews contain the given customerId
        let devices = await device.find({
            'reviews.customerId': customerId
        }).populate({
            path: 'reviews.customerId',
            select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
        });

        // Filter reviews to only include those with the matching customerId
        devices = devices.map(device => {
            device.reviews = device.reviews.filter(review => {
                return review.customerId._id.toString() === customerId;
            });
            return device;
        });

        res.json(devices);
    } catch (error) {
        console.error("Error fetching devices:", error);
        res.status(500).json({ message: "Server error" });
    }
};
// deviceRouter.put('/:deviceId/favorites/:customerId',
export const addFavorite = async (req, res) => {
    try {
      const { deviceId, customerId } = req.params; // Destructure params for clarity
      
      console.log("deviceId, customerId : " , deviceId, customerId);
      // Validate required parameters
      if (!deviceId || !customerId) {
        return res.status(400).json({ error: 'Missing required parameters: deviceId and customerId' });
      }

      const Device = await device.findById(deviceId); // Find the device by ID
  
      if (!Device) {
        return res.status(404).json({ error: 'Device not found' });
      }
  
      const existingFavorite = Device.favorites.some(favorite => favorite.customerId.toString() == customerId);
  
      if (existingFavorite) {
        // If the device is already in favorites, remove it
        Device.favorites = Device.favorites.filter(favorite => favorite.customerId.toString() !== customerId);
      } else {
          
          // If the device is not in favorites, add it
          Device.favorites.push({ customerId: customerId });
        }
        console.log("Device : ",Device);
        
      await Device.save(); // Save the changes to the device
  
      const updatedDevice = await device.findById(Device).populate('favorites.customerId'); // Populate customer details
  
      res.status(200).json({ status: true, data: updatedDevice });
    } catch (error) {
      console.error(error); // Log the error for debugging
      res.status(500).json({ error: 'Internal server error' });
    }
  };
  
  export const getFavoriteByCutomerId = async (req, res) => {
    try {
      const { customerId } = req.params;
  
      // Validate required parameter
      if (!customerId) {
        return res.status(400).json({ error: 'Missing required parameter: customerId' });
      }
  
      const Devices = await device.find().populate('favorites.customerId');
      console.log("Devices : ",Devices);
      
      const favoriteDevices = Devices.filter(device => device.favorites.some(favorite =>   favorite?.customerId?._id.toString() === customerId));
  
      res.json(favoriteDevices);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };

// export const getallcustomer = async (req, res) => {
//     const { customerId } = req.params;

//     // Validate customerId
//     if (!mongoose.Types.ObjectId.isValid(customerId)) {
//         return res.status(400).json({ message: "Invalid customer ID format." });
//     }

//     try {
//         // Find all devices where reviews contain the given customerId
//         let devices = await device.find({
//             'reviews.customerId': customerId
//         }).populate({
//             path: 'reviews.customerId',
//             select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
//         });

//         // Filter reviews to only include those with the matching customerId
//         devices = devices.map(device => {
//             device.reviews = device.reviews
//                 .filter(review => review.customerId._id.toString() === customerId)
//                 .map(review => {
//                     // Clone the review object to avoid mutating Mongoose documents directly
//                     const clonedReview = {
//                         ...review.toObject(),
//                         // Calculate the total points and average score
//                         averageScore: ((review.food + review.service + review.ambiance) / 3).toFixed(2)
//                     };
//                     return clonedReview;
//                 });

//             return device;
//         });

//         res.json(devices);
//     } catch (error) {
//         console.error("Error fetching devices:", error);
//         res.status(500).json({ message: "Server error" });
//     }
// };




export const deleteDevice = async (req, res) => {
    console.log(req.params)
    let data = await device.deleteOne(req.params)
    if (data) {
        res.send({ message: "device data delete successfully" });
    }
    else {
        res.send({ message: "device data cannot delete successfully" })
    }
}
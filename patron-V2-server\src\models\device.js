import mongoose from 'mongoose';
var current = new Date();
const timeStamp = new Date(Date.UTC(current.getFullYear(),
    current.getMonth(), current.getDate(), current.getHours(),
    current.getMinutes(), current.getSeconds(), current.getMilliseconds()));


    const reviewSchema = new mongoose.Schema({
        // Keep old fields for backward compatibility
        food: {
            type: Number,
            required: false
        },
        service: {
            type: Number,
            required: false
        },
        ambiance: {
            type: Number,
            required: false
        },
        // New simple 5-star rating field
        rating: {
            type: Number,
            required: true,
            min: 1,
            max: 5
        },
        customerId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'PatronPalCustomer'
        },
        testimonial: {
            type: String,
            required: false
        },
        orderId: {
            type: String,
            required: false
        },
        createdDate: {
            type: Date,
            default: timeStamp
        }
    });
const deivceSchema = new mongoose.Schema({
    name: {
        type: String
    },
    active: {
        type: String,
        default: true
    },
    Line1: {
        type: String
    },
    Line2: {
        type: String
    },
    City: {
        type: String
    },
    Phoneno: {
        type: String
    },
    State: {
        type: String
    },
    PostalCode: {
        type: String
    },
    Country: {
        type: String
    },
    image: {
        type: String
    },
    businessType: {
        type: String
    },
    delivery: {
        type: String,
        default: false
    },
    deliveryStartTime:{
        type:String
    },
    deliveryEndTime:{
        type:String
    },
    ChargesperKm: {
        type: Number
    },
    ChargesFreeKm:{
        type: Number
    },
    pickupStartTime:{
        type:String
    },
    Streetaddress:{
        type:String
    },
    pickupEndTime:{
        type:String
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'user'
    },
    createdDate: {
        type: Date,
        default: timeStamp
    },
    reviews: [reviewSchema],
    favorites: [{
        customerId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'PatronPalCustomer'
        }
    }]

})
const device = mongoose.model("device", deivceSchema);
export default device;